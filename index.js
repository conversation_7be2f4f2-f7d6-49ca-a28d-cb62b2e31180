import 'dotenv/config';
import * as Sentry from '@sentry/node';
import kafkaConnection from './connection/kafka.js';
import postgresConnection from './connection/postgres.js';
import { isValidMessagePacket } from './helper/index.js';
import NotificationFacade from './service/NotificationFacade.js';
import kafkaConfig from './config/kafka.js';
import logger from './utils/logger.js';

async function onMessageHandler(message) {
  try {
    if (!isValidMessagePacket(message)) {
      return;
    }

    // TODO: Remove this early return - draining kafka without sending emails
    return;

    const notificationFacade = new NotificationFacade();
    await notificationFacade.sendNotification(message);
  } catch (error) {
    error.message = `Message processing error: ${error.message}`;
    throw error;
  }
}

const initializeService = async () => {
  try {
    await postgresConnection.initialize();
    await kafkaConnection.connect();
    await kafkaConnection.subscribe(kafkaConfig.topics.emailNotification, onMessageHandler);

    logger.info('🧩 Email notification service started successfully');
  } catch (error) {
    logger.error(`Failed to initialize email notification service`, { error });
    await Sentry.flush();
    process.exit(1);
  }
};

// Graceful shutdown
const gracefulShutdown = async (signal) => {
  logger.info(`${signal} received, shutting down gracefully`);
  try {
    await kafkaConnection.disconnect();
    await postgresConnection.close();
    logger.info('Graceful shutdown completed');
  } catch (error) {
    logger.error('Error during shutdown', { error });
  }
  process.exit(0);
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

initializeService();
